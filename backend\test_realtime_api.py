#!/usr/bin/env python3
"""
Test script to demonstrate DocTranscribe real-time API functionality
"""

import requests
import json
import base64
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000/api/v1"

def test_consultation_workflow():
    """Test the complete consultation workflow"""
    print("🏥 Testing DocTranscribe Real-time API")
    print("=" * 50)
    
    # Step 1: Start consultation
    print("\n1. Starting consultation...")
    consultation_data = {
        "patient_info": {
            "name": "John Doe",
            "age": 45,
            "gender": "Male",
            "mrn": "MRN123456",
            "reason_for_visit": "Regular checkup"
        },
        "doctor_name": "<PERSON><PERSON> <PERSON>",
        "language": "en-US"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/consultations/start", json=consultation_data)
        if response.status_code == 200:
            result = response.json()
            consultation_id = result["consultation_id"]
            print(f"✅ Consultation started: {consultation_id}")
        else:
            print(f"❌ Failed to start consultation: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error starting consultation: {e}")
        return
    
    # Step 2: Simulate audio chunks
    print(f"\n2. Simulating audio recording...")
    
    # Create dummy audio data (in real app, this would be actual audio)
    dummy_audio = b"dummy_audio_data_chunk"
    audio_b64 = base64.b64encode(dummy_audio).decode('utf-8')
    
    for i in range(5):
        print(f"   Sending audio chunk {i+1}/5...")
        
        chunk_data = {
            "consultation_id": consultation_id,
            "audio_data": audio_b64,
            "chunk_index": i,
            "is_final": i == 4
        }
        
        try:
            response = requests.post(f"{BASE_URL}/audio/chunk", json=chunk_data)
            if response.status_code == 200:
                result = response.json()
                if result.get("transcript"):
                    print(f"   📝 Transcript: {result['transcript']}")
                else:
                    print(f"   ⏳ Processing chunk {i+1}...")
            else:
                print(f"   ⚠️  Chunk {i+1} failed: {response.text}")
        except Exception as e:
            print(f"   ❌ Error sending chunk {i+1}: {e}")
        
        time.sleep(0.5)  # Simulate real-time delay
    
    # Step 3: Stop recording
    print(f"\n3. Stopping recording...")
    try:
        response = requests.post(f"{BASE_URL}/audio/stop",
                               headers={"X-Consultation-ID": consultation_id})
        if response.status_code == 200:
            print("✅ Recording stopped successfully")
        else:
            print(f"⚠️  Stop recording response: {response.text}")
    except Exception as e:
        print(f"❌ Error stopping recording: {e}")
    
    # Step 4: Finish consultation
    print(f"\n4. Finishing consultation...")
    try:
        response = requests.post(f"{BASE_URL}/consultations/finish",
                               headers={"X-Consultation-ID": consultation_id})
        if response.status_code == 200:
            result = response.json()
            print("✅ Consultation finished successfully")
            print(f"   Duration: {result.get('duration', 'N/A')} seconds")
        else:
            print(f"⚠️  Finish consultation response: {response.text}")
    except Exception as e:
        print(f"❌ Error finishing consultation: {e}")
    
    # Step 5: Generate PDF
    print(f"\n5. Generating PDF report...")
    pdf_data = {
        "consultation_id": consultation_id,
        "doctor_notes": "Patient appears healthy. Recommend follow-up in 6 months.",
        "include_timestamps": True,
        "include_speaker_labels": True
    }
    
    try:
        response = requests.post(f"{BASE_URL}/pdf/generate", json=pdf_data)
        if response.status_code == 200:
            result = response.json()
            print("✅ PDF generated successfully")
            print(f"   PDF Path: {result.get('pdf_path', 'N/A')}")
        else:
            print(f"⚠️  PDF generation response: {response.text}")
    except Exception as e:
        print(f"❌ Error generating PDF: {e}")
    
    print(f"\n🎉 Test completed! Consultation ID: {consultation_id}")

def test_health_check():
    """Test API health"""
    print("\n🔍 Checking API health...")
    try:
        response = requests.get(f"{BASE_URL.replace('/api/v1', '')}/health")
        if response.status_code == 200:
            result = response.json()
            print("✅ API is healthy")
            print(f"   Services: {result.get('services', {})}")
        else:
            print(f"❌ Health check failed: {response.text}")
    except Exception as e:
        print(f"❌ Error checking health: {e}")

if __name__ == "__main__":
    # Test health first
    test_health_check()
    
    # Run the full workflow test
    test_consultation_workflow()
    
    print("\n" + "=" * 50)
    print("🚀 You can now:")
    print("   1. Install Flutter to run the mobile app")
    print("   2. Use the API docs at http://localhost:8000/docs")
    print("   3. Build your own client application")
    print("   4. Test with real audio files")
