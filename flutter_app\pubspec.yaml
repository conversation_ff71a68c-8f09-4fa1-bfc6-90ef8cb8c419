name: doctranscribe
description: Medical consultation transcription and PDF generation app
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # Audio Recording
  record: ^5.0.4
  permission_handler: ^11.0.1
  
  # PDF Generation
  pdf: ^3.10.7
  printing: ^5.11.1
  
  # File & Storage
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2
  
  # UI & Navigation
  go_router: ^12.1.1
  flutter_svg: ^2.0.9
  
  # Utilities
  intl: ^0.18.1
  uuid: ^4.1.0
  
  # Development
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true

  # assets:
  #   - assets/images/
  #   - assets/icons/

  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
