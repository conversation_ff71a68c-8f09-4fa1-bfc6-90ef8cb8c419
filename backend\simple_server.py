#!/usr/bin/env python3
"""
Simple DocTranscribe server for testing
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List
import uvicorn
import uuid
from datetime import datetime

# Simple data models
class PatientInfo(BaseModel):
    name: str
    age: int
    gender: str
    mrn: Optional[str] = None
    reason_for_visit: str

class StartConsultationRequest(BaseModel):
    patient_info: PatientInfo
    doctor_name: Optional[str] = None
    language: str = "en-US"

class StartConsultationResponse(BaseModel):
    consultation_id: str
    status: str
    message: str

class AudioChunkRequest(BaseModel):
    audio_data: str
    chunk_index: int
    is_final: bool = False

class AudioChunkResponse(BaseModel):
    consultation_id: str
    chunk_index: int
    transcript: Optional[str] = None
    is_partial: bool = True
    confidence: Optional[float] = None

class FinishConsultationRequest(BaseModel):
    doctor_notes: Optional[str] = None

class FinishConsultationResponse(BaseModel):
    consultation_id: str
    status: str
    transcript_segments: List[dict]
    summary_stats: dict
    processing_time_ms: float
    message: str

# Create FastAPI app
app = FastAPI(
    title="DocTranscribe API",
    description="Medical consultation transcription service",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage
consultations = {}

@app.get("/")
async def root():
    return {
        "status": "healthy",
        "message": "DocTranscribe API is running",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "services": {
            "api": "running",
            "nvidia_riva": "configured"
        }
    }

@app.post("/api/v1/consultations/start", response_model=StartConsultationResponse)
async def start_consultation(request: StartConsultationRequest):
    consultation_id = str(uuid.uuid4())
    
    consultation_data = {
        "id": consultation_id,
        "patient_info": request.patient_info.dict(),
        "doctor_name": request.doctor_name,
        "language": request.language,
        "status": "created",
        "start_time": datetime.utcnow().isoformat(),
        "transcript_segments": [],
        "audio_chunks": []
    }
    
    consultations[consultation_id] = consultation_data
    
    return StartConsultationResponse(
        consultation_id=consultation_id,
        status="created",
        message="Consultation started successfully"
    )

@app.post("/api/v1/audio/chunk", response_model=AudioChunkResponse)
async def process_audio_chunk(
    request: AudioChunkRequest,
    x_consultation_id: str = None
):
    if not x_consultation_id or x_consultation_id not in consultations:
        raise HTTPException(status_code=404, detail="Consultation not found")
    
    # Simulate transcription
    mock_transcript = f"Mock transcription for chunk {request.chunk_index}"
    
    # Update consultation status to recording
    consultations[x_consultation_id]["status"] = "recording"
    
    return AudioChunkResponse(
        consultation_id=x_consultation_id,
        chunk_index=request.chunk_index,
        transcript=mock_transcript,
        is_partial=not request.is_final,
        confidence=0.85
    )

@app.post("/api/v1/audio/stop")
async def stop_recording(x_consultation_id: str = None):
    if not x_consultation_id or x_consultation_id not in consultations:
        raise HTTPException(status_code=404, detail="Consultation not found")
    
    consultations[x_consultation_id]["status"] = "recorded"
    
    return {"message": "Recording stopped successfully"}

@app.post("/api/v1/consultations/finish", response_model=FinishConsultationResponse)
async def finish_consultation(
    request: FinishConsultationRequest,
    x_consultation_id: str = None
):
    if not x_consultation_id or x_consultation_id not in consultations:
        raise HTTPException(status_code=404, detail="Consultation not found")
    
    # Mock transcript segments
    mock_segments = [
        {
            "start_time": 0.0,
            "end_time": 5.0,
            "speaker": "doctor",
            "text": "Good morning, how are you feeling today?",
            "confidence": 0.95
        },
        {
            "start_time": 5.0,
            "end_time": 10.0,
            "speaker": "patient",
            "text": "I've been having some headaches lately.",
            "confidence": 0.90
        }
    ]
    
    mock_stats = {
        "total_duration": 10.0,
        "word_count": 15,
        "speaker_stats": {
            "doctor": {"duration": 5.0, "word_count": 8},
            "patient": {"duration": 5.0, "word_count": 7}
        }
    }
    
    consultations[x_consultation_id]["status"] = "completed"
    consultations[x_consultation_id]["transcript_segments"] = mock_segments
    consultations[x_consultation_id]["doctor_notes"] = request.doctor_notes
    
    return FinishConsultationResponse(
        consultation_id=x_consultation_id,
        status="completed",
        transcript_segments=mock_segments,
        summary_stats=mock_stats,
        processing_time_ms=1500.0,
        message="Consultation finished successfully"
    )

@app.get("/api/v1/consultations/{consultation_id}/status")
async def get_consultation_status(consultation_id: str):
    if consultation_id not in consultations:
        raise HTTPException(status_code=404, detail="Consultation not found")
    
    consultation = consultations[consultation_id]
    
    return {
        "consultation_id": consultation_id,
        "status": consultation["status"],
        "patient_name": consultation["patient_info"]["name"],
        "doctor_name": consultation.get("doctor_name"),
        "start_time": consultation.get("start_time"),
        "transcript_count": len(consultation.get("transcript_segments", [])),
        "has_pdf": False
    }

@app.get("/api/v1/consultations/")
async def list_consultations():
    result = []
    for consultation_id, consultation in consultations.items():
        result.append({
            "consultation_id": consultation_id,
            "status": consultation["status"],
            "patient_name": consultation["patient_info"]["name"],
            "doctor_name": consultation.get("doctor_name"),
            "start_time": consultation.get("start_time"),
            "transcript_count": len(consultation.get("transcript_segments", [])),
            "has_pdf": False
        })
    return result

@app.post("/api/v1/pdf/generate")
async def generate_pdf(
    request: dict,
    x_consultation_id: str = None
):
    if not x_consultation_id or x_consultation_id not in consultations:
        raise HTTPException(status_code=404, detail="Consultation not found")
    
    return {
        "consultation_id": x_consultation_id,
        "pdf_filename": f"consultation_{x_consultation_id[:8]}.pdf",
        "file_size_bytes": 1024000,
        "generation_time_ms": 2000.0,
        "message": "PDF generated successfully (mock)"
    }

if __name__ == "__main__":
    print("🏥 Starting DocTranscribe Simple Server...")
    print("🌐 Server will be available at: http://localhost:8001")
    print("📚 API documentation: http://localhost:8001/docs")
    print()

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        reload=False
    )
